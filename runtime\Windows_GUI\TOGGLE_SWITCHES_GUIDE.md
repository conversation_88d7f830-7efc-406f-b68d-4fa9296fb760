# 切换开关功能指南 - PyQt5手势文本输入GUI v2.2

## 🎯 新增功能概述

在右下区域的当前输入结果显示下方，新增了两个Fluent Design风格的智能切换开关，提供高级的文本输入辅助功能。

## 🔧 切换开关详细说明

### 1. 限制词汇范围开关 ("限制词汇范围")

#### 📍 位置
- 右下区域，当前输入结果显示下方
- 左侧第一个切换开关

#### ⚙️ 功能说明

**开启状态 (ON)**:
- ✅ **限制识别范围**: 只从300单词显示区域中匹配单词
- ✅ **激活单词区域**: 300单词区域变为正常亮度，可交互状态
- ✅ **最接近匹配**: 使用`difflib`算法找到最相似的单词
- ✅ **实时处理**: 当前输入会自动匹配到最接近的词汇

**关闭状态 (OFF - 默认)**:
- ❌ **无限制识别**: 允许从任何词汇源进行识别
- ❌ **禁用单词区域**: 300单词区域变暗，不可交互
- ❌ **原始输入**: 显示原始的输入结果

#### 🔍 技术实现
```python
def find_closest_word(self, input_text: str, vocabulary_list: list) -> str:
    """找到词汇表中最接近的单词"""
    closest_matches = difflib.get_close_matches(
        input_text, vocabulary_list, n=1, cutoff=0.3
    )
    return closest_matches[0] if closest_matches else input_text
```

### 2. 历史记录辅助开关 ("历史记录辅助")

#### 📍 位置
- 右下区域，当前输入结果显示下方
- 右侧第二个切换开关

#### ⚙️ 功能说明

**开启状态 (ON)**:
- ✅ **历史记录匹配**: 基于历史输入记录进行智能修正
- ✅ **前缀匹配**: 检查历史记录中以当前输入开头的单词
- ✅ **最近优先**: 优先匹配最近的10个历史记录
- ✅ **自动补全**: 自动补全到匹配的历史单词

**关闭状态 (OFF - 默认)**:
- ❌ **禁用历史辅助**: 不使用历史记录进行修正
- ❌ **原始输入**: 显示原始的输入结果

#### 🔍 技术实现
```python
def apply_history_correction(self, current_input: str, history_list: list) -> str:
    """基于历史记录进行单词修正"""
    for history_word in reversed(history_list[-10:]):
        if history_word.lower().startswith(current_input.lower()):
            return history_word
    return current_input
```

## 🎨 Fluent Design样式特性

### 切换开关外观
- **尺寸**: 50x24像素的圆角矩形
- **颜色方案**:
  - 关闭状态: #E1E1E1 (浅灰色)
  - 开启状态: #0078D4 (Fluent蓝)
  - 悬停效果: #106EBE (深蓝色)
- **动画效果**: 平滑的状态切换过渡
- **字体**: Microsoft YaHei 12px

### 布局设计
- **水平排列**: 两个开关在同一行
- **间距**: 20px的开关间距
- **对齐**: 左对齐，右侧留有弹性空间
- **边距**: 上下10px，左右5px的容器边距

## 🚀 使用场景示例

### 场景1: 精确词汇匹配
1. **开启"限制词汇范围"**
2. 300单词区域变亮，可以看到所有可用词汇
3. 输入"hel"时，自动匹配到"hello"
4. 显示完整的匹配单词而不是部分输入

### 场景2: 智能历史补全
1. **开启"历史记录辅助"**
2. 先输入几个完整单词到历史记录
3. 再次输入相同单词的前几个字母
4. 系统自动补全到历史记录中的完整单词

### 场景3: 双重辅助模式
1. **同时开启两个开关**
2. 首先尝试历史记录匹配
3. 如果历史记录无匹配，则使用词汇表匹配
4. 提供最智能的输入辅助体验

## 🔍 调试和监控

### 控制台输出
程序提供详细的调试信息：
```
词汇范围限制: 开启
300单词区域已激活
历史记录辅助: 开启
词汇限制: 'hel' -> 'hello'
历史记录匹配: hel -> hello
```

### 状态指示
- **单词区域状态**: 通过颜色变化指示激活/禁用状态
- **输入结果**: 通过文字颜色区分原始输入和处理后结果
- **开关状态**: 通过开关位置和颜色指示当前状态

## 🛠️ API接口

### 外部调用接口
```python
# 词汇匹配接口
result = gui.find_closest_word("输入文本", ["词汇", "列表"])

# 历史修正接口  
result = gui.apply_history_correction("当前输入", ["历史", "记录"])

# 获取当前词汇表
vocabulary = gui.get_current_vocabulary()
```

### 事件回调
```python
# 监听开关状态变化
gui.vocabulary_constraint_toggle.stateChanged.connect(callback_function)
gui.history_assistance_toggle.stateChanged.connect(callback_function)
```

## 🎯 最佳实践

### 推荐使用方式
1. **初学者**: 只开启"限制词汇范围"，专注于已知词汇
2. **进阶用户**: 开启"历史记录辅助"，利用输入历史
3. **专业用户**: 同时开启两个功能，获得最佳体验

### 性能优化
- 历史记录只检查最近10个条目，避免性能问题
- 词汇匹配使用0.3的相似度阈值，平衡准确性和性能
- 状态变化时提供即时反馈，提升用户体验

## 🔄 版本兼容性

- **向后兼容**: 所有原有功能保持不变
- **默认状态**: 两个开关默认关闭，不影响现有工作流
- **渐进增强**: 用户可以根据需要逐步启用新功能

这些切换开关为手势文本输入系统提供了强大的智能辅助功能，同时保持了界面的简洁和易用性。

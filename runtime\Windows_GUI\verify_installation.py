#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证PyQt5版本安装和功能
"""

def verify_installation():
    """验证安装"""
    print("验证PyQt5手势文本输入GUI系统...")
    
    # 检查PyQt5
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow
        print("✓ PyQt5 已安装")
    except ImportError:
        print("✗ PyQt5 未安装")
        return False
    
    # 检查pyqtgraph
    try:
        import pyqtgraph as pg
        print("✓ pyqtgraph 已安装")
    except ImportError:
        print("✗ pyqtgraph 未安装")
        return False
    
    # 检查numpy
    try:
        import numpy as np
        print("✓ numpy 已安装")
    except ImportError:
        print("✗ numpy 未安装")
        return False
    
    # 检查主程序文件
    try:
        import os
        if os.path.exists('gesture_input_gui.py'):
            print("✓ 主程序文件存在")
        else:
            print("✗ 主程序文件不存在")
            return False
    except:
        print("✗ 文件检查失败")
        return False
    
    print("\n所有依赖已正确安装！")
    print("可以运行: python gesture_input_gui.py")
    return True

if __name__ == "__main__":
    verify_installation()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PyQt5版本的手势文本输入GUI
验证所有功能是否正常工作
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有必要的导入"""
    print("🔍 测试导入...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5.QtWidgets 导入成功")
    except ImportError as e:
        print(f"❌ PyQt5.QtWidgets 导入失败: {e}")
        return False
    
    try:
        import pyqtgraph as pg
        print("✅ pyqtgraph 导入成功")
    except ImportError as e:
        print(f"❌ pyqtgraph 导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print("✅ numpy 导入成功")
    except ImportError as e:
        print(f"❌ numpy 导入失败: {e}")
        return False
    
    try:
        import matplotlib.colors as mcolors
        print("✅ matplotlib.colors 导入成功")
    except ImportError as e:
        print(f"❌ matplotlib.colors 导入失败: {e}")
        return False
    
    return True

def test_gui_creation():
    """测试GUI创建"""
    print("\n🏗️ 测试GUI创建...")
    
    try:
        from gesture_input_gui import GestureInputGUI, create_parula_colormap, WordButton
        print("✅ 主要类导入成功")
        
        # 测试parula colormap创建
        parula_colors = create_parula_colormap()
        if len(parula_colors) == 64:
            print("✅ Parula colormap创建成功 (64色)")
        else:
            print(f"⚠️ Parula colormap颜色数量异常: {len(parula_colors)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ GUI类导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ GUI创建测试失败: {e}")
        return False

def test_functionality():
    """测试功能性"""
    print("\n⚙️ 测试功能...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gesture_input_gui import GestureInputGUI
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建GUI实例
        gui = GestureInputGUI()
        print("✅ GUI实例创建成功")
        
        # 测试数据生成
        words = gui.generate_sample_words()
        if len(words) == 300:
            print("✅ 示例单词生成成功 (300个)")
        else:
            print(f"⚠️ 示例单词数量异常: {len(words)}")
        
        # 测试方法存在性
        methods_to_test = [
            'update_word_display',
            'update_current_input_display', 
            'on_word_click',
            'clear_history',
            'add_to_history',
            'remove_word',
            'update_plot_data',
            'start_recording',
            'stop_recording',
            'play_recording',
            'clear_plot'
        ]
        
        for method_name in methods_to_test:
            if hasattr(gui, method_name):
                print(f"✅ 方法 {method_name} 存在")
            else:
                print(f"❌ 方法 {method_name} 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试PyQt5版本的手势文本输入GUI\n")
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败，请安装必要的依赖包")
        print("运行: pip install PyQt5 pyqtgraph matplotlib numpy")
        return False
    
    # 测试GUI创建
    if not test_gui_creation():
        print("\n❌ GUI创建测试失败")
        return False
    
    # 测试功能
    if not test_functionality():
        print("\n❌ 功能测试失败")
        return False
    
    print("\n🎉 所有测试通过！")
    print("\n📋 功能特性验证:")
    print("✅ PyQt5 GUI框架")
    print("✅ pyqtgraph 高性能绘图")
    print("✅ 自定义parula colormap")
    print("✅ Microsoft Fluent Design样式")
    print("✅ 自定义WordButton类")
    print("✅ 响应式布局设计")
    print("✅ 富文本显示支持")
    print("✅ 交互式数据可视化")
    
    print("\n🎯 可以安全运行主程序:")
    print("python gesture_input_gui.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

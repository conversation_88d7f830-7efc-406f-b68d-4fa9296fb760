#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PyQt5版本的键盘输入功能
验证实时字符输入、切换开关集成和焦点管理
"""

import sys
import os

def test_keyboard_implementation():
    """测试键盘输入实现"""
    print("🔍 测试键盘输入实现...")
    
    try:
        with open('gesture_input_gui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查键盘事件处理方法
        if 'def keyPressEvent(self, event):' in content:
            print("✅ keyPressEvent方法已实现")
        else:
            print("❌ keyPressEvent方法缺失")
        
        # 检查焦点管理
        if 'setFocusPolicy(Qt.StrongFocus)' in content and 'setFocus()' in content:
            print("✅ 焦点管理已实现")
        else:
            print("❌ 焦点管理缺失")
        
        # 检查键盘输入状态变量
        if 'current_typed_input' in content and 'is_typing' in content:
            print("✅ 键盘输入状态变量已添加")
        else:
            print("❌ 键盘输入状态变量缺失")
        
        # 检查字母输入处理
        if 'text.isalpha()' in content:
            print("✅ 字母输入过滤已实现")
        else:
            print("❌ 字母输入过滤缺失")
        
        # 检查特殊按键处理
        special_keys = ['Key_Backspace', 'Key_Return', 'Key_Enter', 'Key_Space', 'Key_Escape']
        missing_keys = []
        for key in special_keys:
            if key not in content:
                missing_keys.append(key)
        
        if not missing_keys:
            print("✅ 特殊按键处理已实现")
        else:
            print(f"❌ 缺失特殊按键处理: {missing_keys}")
        
        # 检查实时更新方法
        if 'update_current_input_from_keyboard' in content:
            print("✅ 实时更新方法已实现")
        else:
            print("❌ 实时更新方法缺失")
        
        # 检查单词完成方法
        if 'complete_word_input' in content:
            print("✅ 单词完成方法已实现")
        else:
            print("❌ 单词完成方法缺失")
        
        return True
        
    except FileNotFoundError:
        print("❌ 主程序文件不存在")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_integration_with_toggles():
    """测试与切换开关的集成"""
    print("\n🔍 测试与切换开关的集成...")
    
    try:
        with open('gesture_input_gui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查词汇限制集成
        if 'vocabulary_constraint_enabled' in content and 'find_closest_word' in content:
            print("✅ 词汇限制功能集成已实现")
        else:
            print("❌ 词汇限制功能集成缺失")
        
        # 检查历史记录辅助集成
        if 'history_assistance_enabled' in content and 'apply_history_correction' in content:
            print("✅ 历史记录辅助集成已实现")
        else:
            print("❌ 历史记录辅助集成缺失")
        
        # 检查视觉反馈
        if 'certain_part' in content and 'uncertain_part' in content:
            print("✅ 视觉反馈（黑色/灰色字符）已实现")
        else:
            print("❌ 视觉反馈缺失")
        
        # 检查建议显示
        if 'suggested_word' in content and 'startswith' in content:
            print("✅ 智能建议显示已实现")
        else:
            print("❌ 智能建议显示缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_user_interface_updates():
    """测试用户界面更新"""
    print("\n🔍 测试用户界面更新...")
    
    try:
        with open('gesture_input_gui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查说明文字更新
        if '键盘输入：直接输入字母进行实时测试' in content:
            print("✅ 键盘输入说明已添加")
        else:
            print("❌ 键盘输入说明缺失")
        
        # 检查操作指南
        if '按空格或回车完成单词输入' in content and '按退格删除字符' in content:
            print("✅ 操作指南已添加")
        else:
            print("❌ 操作指南缺失")
        
        # 检查鼠标焦点处理
        if 'mousePressEvent' in content and 'setFocus' in content:
            print("✅ 鼠标焦点处理已实现")
        else:
            print("❌ 鼠标焦点处理缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_functionality():
    """测试功能性"""
    print("\n⚙️ 测试功能...")
    
    try:
        from gesture_input_gui import GestureInputGUI
        
        # 创建GUI实例（不显示）
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        gui = GestureInputGUI()
        
        # 测试键盘输入状态变量
        if hasattr(gui, 'current_typed_input') and hasattr(gui, 'is_typing'):
            print("✅ 键盘输入状态变量可用")
        else:
            print("❌ 键盘输入状态变量缺失")
        
        # 测试键盘处理方法
        if hasattr(gui, 'keyPressEvent') and hasattr(gui, 'update_current_input_from_keyboard'):
            print("✅ 键盘处理方法可用")
        else:
            print("❌ 键盘处理方法缺失")
        
        # 测试单词完成方法
        if hasattr(gui, 'complete_word_input'):
            print("✅ 单词完成方法可用")
        else:
            print("❌ 单词完成方法缺失")
        
        # 测试焦点策略
        if gui.focusPolicy() == gui.StrongFocus:
            print("✅ 焦点策略设置正确")
        else:
            print("❌ 焦点策略设置错误")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 测试PyQt5版本的键盘输入功能\n")
    
    # 测试实现
    if not test_keyboard_implementation():
        print("\n❌ 键盘输入实现测试失败")
        return False
    
    # 测试集成
    if not test_integration_with_toggles():
        print("\n❌ 切换开关集成测试失败")
        return False
    
    # 测试界面更新
    if not test_user_interface_updates():
        print("\n❌ 用户界面更新测试失败")
        return False
    
    # 测试功能
    if not test_functionality():
        print("\n❌ 功能测试失败")
        return False
    
    print("\n🎉 所有键盘输入功能测试通过！")
    print("\n📋 功能确认:")
    print("✅ 实时字母输入处理")
    print("✅ 特殊按键支持（退格、回车、空格、Esc）")
    print("✅ 焦点管理和键盘事件捕获")
    print("✅ 与切换开关功能完整集成")
    print("✅ 智能建议和视觉反馈")
    print("✅ 单词完成和历史记录添加")
    print("✅ 用户界面说明更新")
    
    print("\n🎯 使用说明:")
    print("• 启动程序后，窗口会自动获得键盘焦点")
    print("• 直接输入字母，会在当前输入区域实时显示")
    print("• 开启切换开关可以看到智能建议效果")
    print("• 按空格或回车完成单词输入并添加到历史")
    print("• 按退格删除字符，按Esc清空当前输入")
    print("• 点击窗口任意位置可重新获得键盘焦点")
    
    print("\n🎯 程序已准备就绪:")
    print("python gesture_input_gui.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

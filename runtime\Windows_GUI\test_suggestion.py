#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试建议窗口功能的简单脚本
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import Qt, QPoint
from gesture_input_gui import SuggestionWindow

class TestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试建议窗口")
        self.setGeometry(300, 300, 400, 200)
        
        # 创建建议窗口
        self.suggestion_window = SuggestionWindow(self)
        
        # 创建布局
        layout = QVBoxLayout()
        
        # 添加说明标签
        label = QLabel("点击按钮测试建议窗口功能")
        layout.addWidget(label)
        
        # 添加测试按钮
        btn1 = QPushButton("显示建议: 'hello'")
        btn1.clicked.connect(self.show_suggestion1)
        layout.addWidget(btn1)
        
        btn2 = QPushButton("显示建议: 'world'")
        btn2.clicked.connect(self.show_suggestion2)
        layout.addWidget(btn2)
        
        btn3 = QPushButton("隐藏建议")
        btn3.clicked.connect(self.hide_suggestion)
        layout.addWidget(btn3)
        
        self.setLayout(layout)
    
    def show_suggestion1(self):
        pos = self.mapToGlobal(QPoint(50, 150))
        self.suggestion_window.show_suggestion("hello", pos)
    
    def show_suggestion2(self):
        pos = self.mapToGlobal(QPoint(200, 150))
        self.suggestion_window.show_suggestion("world", pos)
    
    def hide_suggestion(self):
        self.suggestion_window.hide_suggestion()

def main():
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
